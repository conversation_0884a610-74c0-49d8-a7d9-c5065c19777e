<!DOCTYPE html>
<html>
<head>
    <title>KPI Update Test</title>
</head>
<body>
    <h1>KPI Update Test</h1>
    
    <div>
        <h2>Test KPI Elements:</h2>
        <div>Total Check-ins: <span id="totalCheckins">-</span></div>
        <div>Total Hours: <span id="totalHours">-</span></div>
        <div>Active Tutors: <span id="activeTutors">-</span></div>
        <div>Avg Session Duration: <span id="avgSessionDuration">-</span></div>
    </div>
    
    <button onclick="testKPIUpdate()">Test KPI Update</button>
    <button onclick="testAPICall()">Test API Call</button>
    
    <div id="debug-output"></div>
    
    <script>
        function testKPIUpdate() {
            console.log("Testing KPI update with mock data...");
            
            const mockSummary = {
                total_checkins: 492,
                total_hours: 1126.0,
                active_tutors: 26,
                avg_session_duration: 2.29
            };
            
            // Test the update function
            updateKPIValues(mockSummary);
        }
        
        function updateKPIValues(summary) {
            console.log("🔧 DEBUG: updateKPIValues called with summary:", summary);
            
            const totalCheckinsEl = document.getElementById("totalCheckins");
            if (totalCheckinsEl) {
                totalCheckinsEl.textContent = summary.total_checkins.toLocaleString();
                console.log("✅ Updated totalCheckins:", summary.total_checkins);
            } else {
                console.error("❌ Element 'totalCheckins' not found");
            }
            
            const totalHoursEl = document.getElementById("totalHours");
            if (totalHoursEl) {
                totalHoursEl.textContent = summary.total_hours + 'h';
                console.log("✅ Updated totalHours:", summary.total_hours);
            } else {
                console.error("❌ Element 'totalHours' not found");
            }
            
            const activeTutorsEl = document.getElementById("activeTutors");
            if (activeTutorsEl) {
                activeTutorsEl.textContent = summary.active_tutors;
                console.log("✅ Updated activeTutors:", summary.active_tutors);
            } else {
                console.error("❌ Element 'activeTutors' not found");
            }
            
            const avgSessionEl = document.getElementById("avgSessionDuration");
            if (avgSessionEl) {
                avgSessionEl.textContent = (summary.avg_session_duration !== '—' ? summary.avg_session_duration + 'h' : '—');
                console.log("✅ Updated avgSessionDuration:", summary.avg_session_duration);
            } else {
                console.error("❌ Element 'avgSessionDuration' not found");
            }
        }
        
        async function testAPICall() {
            console.log("Testing API call...");
            try {
                const response = await fetch('/dashboard-data');
                if (response.ok) {
                    const data = await response.json();
                    console.log("API Response:", data);
                    document.getElementById('debug-output').innerHTML = '<pre>' + JSON.stringify(data.summary, null, 2) + '</pre>';
                    
                    // Update KPIs with real data
                    updateKPIValues(data.summary);
                } else {
                    console.error("API failed:", response.status);
                    document.getElementById('debug-output').innerHTML = 'API failed: ' + response.status;
                }
            } catch (error) {
                console.error("Error:", error);
                document.getElementById('debug-output').innerHTML = 'Error: ' + error.message;
            }
        }
    </script>
</body>
</html>
