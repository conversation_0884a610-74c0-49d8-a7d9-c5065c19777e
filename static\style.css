/* General Layout */
body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-size: 16px;
  margin: 0;
  padding: 0;
  background-color: #f8f9fa; /* Light mode default */
  color: #212529; /* Light mode default text */
  transition: background-color 0.3s, color 0.3s;
}

/* Theme Modes */
/* Default Light Mode - already defined in body */

/* Dark Mode */
html.dark-mode,
body.dark-mode {
  background-color: #121212; /* Dark mode background */
  color: #e0e0e0; /* Dark mode text */
}

/* Blue Mode */
html.blue-mode,
body.blue-mode {
  background-color: #0a192f; /* Deep blue background */
  color: #e6f1ff; /* Light blue text */
}

.blue-mode .card {
  background-color: #172a45; /* Darker blue for cards */
  color: #e6f1ff;
  box-shadow: 0 0.125rem 0.25rem rgba(10, 25, 47, 0.2);
}

.blue-mode .btn-outline-dark {
  color: #64ffda; /* Teal accent */
  border-color: #64ffda;
}

.blue-mode .btn-outline-dark:hover {
  background-color: #64ffda;
  color: #0a192f;
}

.blue-mode .log-month-header,
.blue-mode .log-date-header {
  background-color: #1d3b66; /* Slightly lighter blue for headers */
  color: #ccd6f6;
  border-bottom: 1px solid #233554;
}

.blue-mode .form-control, .blue-mode .form-select {
  background-color: #233554;
  color: #e6f1ff;
  border-color: #1d3b66;
}

.blue-mode .table {
  color: #ccd6f6;
}

.blue-mode .table-striped > tbody > tr:nth-of-type(odd) > * {
  --bs-table-accent-bg: rgba(23, 42, 69, 0.7);
}

/* Purple Mode */
html.purple-mode,
body.purple-mode {
  background-color: #13111c; /* Deep purple background */
  color: #e2e0ec; /* Light purple text */
}

.purple-mode .card {
  background-color: #1e1b2e; /* Darker purple for cards */
  color: #e2e0ec;
  box-shadow: 0 0.125rem 0.25rem rgba(19, 17, 28, 0.2);
}

.purple-mode .btn-outline-dark {
  color: #bd93f9; /* Purple accent */
  border-color: #bd93f9;
}

.purple-mode .btn-outline-dark:hover {
  background-color: #bd93f9;
  color: #13111c;
}

.purple-mode .log-month-header,
.purple-mode .log-date-header {
  background-color: #2d2a3d; /* Slightly lighter purple for headers */
  color: #d8d7e1;
  border-bottom: 1px solid #3d3a4d;
}

.purple-mode .form-control, .purple-mode .form-select {
  background-color: #2d2a3d;
  color: #e2e0ec;
  border-color: #3d3a4d;
}

.purple-mode .table {
  color: #d8d7e1;
}

.purple-mode .table-striped > tbody > tr:nth-of-type(odd) > * {
  --bs-table-accent-bg: rgba(30, 27, 46, 0.7);
}

/* ========== LIGHT MODE TEXT VISIBILITY FIXES ========== */
/* Ensure all text is visible in light mode */
body:not(.dark-mode):not(.blue-mode):not(.purple-mode) {
  color: #212529 !important;
}

/* Light mode text-muted improvements */
body:not(.dark-mode):not(.blue-mode):not(.purple-mode) .text-muted {
  color: #6c757d !important;
  font-weight: 500;
}

/* Light mode small text improvements */
body:not(.dark-mode):not(.blue-mode):not(.purple-mode) small {
  color: #6c757d !important;
  font-weight: 500;
}

/* Light mode card text improvements */
body:not(.dark-mode):not(.blue-mode):not(.purple-mode) .card {
  color: #212529 !important;
}

body:not(.dark-mode):not(.blue-mode):not(.purple-mode) .card-body {
  color: #212529 !important;
}

/* Light mode table text improvements */
body:not(.dark-mode):not(.blue-mode):not(.purple-mode) .table {
  color: #212529 !important;
}

body:not(.dark-mode):not(.blue-mode):not(.purple-mode) .table td,
body:not(.dark-mode):not(.blue-mode):not(.purple-mode) .table th {
  color: #212529 !important;
}

/* Light mode form text improvements */
body:not(.dark-mode):not(.blue-mode):not(.purple-mode) .form-control,
body:not(.dark-mode):not(.blue-mode):not(.purple-mode) .form-select {
  color: #212529 !important;
}

/* Light mode button text improvements */
body:not(.dark-mode):not(.blue-mode):not(.purple-mode) .btn {
  color: inherit !important;
}

/* Light mode specific text color overrides */
body:not(.dark-mode):not(.blue-mode):not(.purple-mode) .text-dark {
  color: #212529 !important;
}

body:not(.dark-mode):not(.blue-mode):not(.purple-mode) .text-secondary {
  color: #6c757d !important;
}

/* Light mode header text improvements */
body:not(.dark-mode):not(.blue-mode):not(.purple-mode) .card-header {
  color: inherit !important;
}

/* Light mode badge text improvements */
body:not(.dark-mode):not(.blue-mode):not(.purple-mode) .badge {
  color: white !important;
}

/* Light mode alert text improvements */
body:not(.dark-mode):not(.blue-mode):not(.purple-mode) .alert {
  color: inherit !important;
}

/* Light mode modal text improvements */
body:not(.dark-mode):not(.blue-mode):not(.purple-mode) .modal-content {
  color: #212529 !important;
}

/* Light mode dropdown text improvements */
body:not(.dark-mode):not(.blue-mode):not(.purple-mode) .dropdown-menu {
  color: #212529 !important;
}

body:not(.dark-mode):not(.blue-mode):not(.purple-mode) .dropdown-item {
  color: #212529 !important;
}

/* Light mode list text improvements */
body:not(.dark-mode):not(.blue-mode):not(.purple-mode) .list-group-item {
  color: #212529 !important;
}

/* Light mode progress text improvements */
body:not(.dark-mode):not(.blue-mode):not(.purple-mode) .progress {
  color: #212529 !important;
}

/* Light mode tooltip text improvements */
body:not(.dark-mode):not(.blue-mode):not(.purple-mode) .tooltip {
  color: #212529 !important;
}

/* Light mode popover text improvements */
body:not(.dark-mode):not(.blue-mode):not(.purple-mode) .popover {
  color: #212529 !important;
}

/* Light mode specific fixes for problematic elements */
body:not(.dark-mode):not(.blue-mode):not(.purple-mode) .dashboard-header {
  color: #212529 !important;
}

body:not(.dark-mode):not(.blue-mode):not(.purple-mode) .metrics-summary {
  color: #212529 !important;
}

body:not(.dark-mode):not(.blue-mode):not(.purple-mode) .metric-card {
  color: #212529 !important;
}

body:not(.dark-mode):not(.blue-mode):not(.purple-mode) .kpi-card {
  color: #212529 !important;
}

/* Light mode text-center improvements */
body:not(.dark-mode):not(.blue-mode):not(.purple-mode) .text-center {
  color: #212529 !important;
}

/* Light mode loading text improvements */
body:not(.dark-mode):not(.blue-mode):not(.purple-mode) [id*="Loading"],
body:not(.dark-mode):not(.blue-mode):not(.purple-mode) [id*="loading"] {
  color: #6c757d !important;
  font-weight: 500;
}

/* Light mode placeholder text improvements */
body:not(.dark-mode):not(.blue-mode):not(.purple-mode) ::placeholder {
  color: #6c757d !important;
  opacity: 1;
}

/* Light mode disabled text improvements */
body:not(.dark-mode):not(.blue-mode):not(.purple-mode) .disabled,
body:not(.dark-mode):not(.blue-mode):not(.purple-mode) [disabled] {
  color: #6c757d !important;
  opacity: 0.65;
}

/* Card + Container Styling */
.card {
  border-radius: 0.5rem; /* Bootstrap's default is often 0.375rem or 0.25rem */
  border: none; /* Remove default borders for cleaner look with shadows */
  background-color: #ffffff;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075); /* Softer shadow */
  transition: background-color 0.3s, color 0.3s, box-shadow 0.3s;
}

.dark-mode .card {
  background-color: #1e1e1e; /* Dark card background */
  color: #e0e0e0;
  box-shadow: 0 0.125rem 0.25rem rgba(255, 255, 255, 0.05); /* Lighter shadow for dark mode */
}

/* Header Sections (Month/Date on Dashboard) */
.log-month-header,
.log-date-header {
  padding: 0.75rem 1.25rem;
  background-color: #e9ecef; /* Light background for headers */
  color: #495057;
  font-weight: 600;
  font-size: 1.05rem;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #dee2e6; /* Separator */
  transition: background-color 0.3s, color 0.3s;
}
.log-month .card-header { border-top-left-radius: 0.5rem; border-top-right-radius: 0.5rem; } /* Match card radius */

.dark-mode .log-month-header,
.dark-mode .log-date-header {
  background-color: #2c2c2c; /* Dark header background */
  color: #adb5bd; /* Lighter text for dark headers */
  border-bottom: 1px solid #3a3a3a;
}

/* Chevron icon animation for collapse */
.chevron-indicator i {
  transition: transform 0.3s ease;
}
.log-month-header[aria-expanded="true"] .chevron-indicator i,
.log-date-header[aria-expanded="true"] .chevron-indicator i {
  transform: rotate(180deg);
}


/* Buttons */
.btn {
  border-radius: 0.375rem; /* Bootstrap 5 default */
  font-weight: 500;
  padding: 0.5rem 1rem; /* Standard BS5 sm button padding */
  transition: all 0.2s ease-in-out;
  /* border: 1px solid transparent; (Bootstrap handles this) */
}

.btn:hover {
  transform: translateY(-1px); /* Subtle lift */
  box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}
.btn:active {
  transform: translateY(0px) scale(0.98); /* Press down effect */
  box-shadow: none;
}

/* Dark mode button outlines */
.dark-mode .btn-outline-dark { color: #adb5bd; border-color: #495057; }
.dark-mode .btn-outline-dark:hover { background-color: #343a40; color: #f8f9fa; }

.dark-mode .btn-outline-primary { color: var(--bs-primary); border-color: var(--bs-primary); } /* Use BS CSS vars */
.dark-mode .btn-outline-primary:hover { background-color: var(--bs-primary); color: #fff; }

.dark-mode .btn-outline-secondary { color: #adb5bd; border-color: #495057; }
.dark-mode .btn-outline-secondary:hover { background-color: #495057; color: #f8f9fa; }

.dark-mode .btn-outline-success { color: var(--bs-success); border-color: var(--bs-success); }
.dark-mode .btn-outline-success:hover { background-color: var(--bs-success); color: #fff; }

.dark-mode .btn-outline-info { color: var(--bs-info); border-color: var(--bs-info); }
.dark-mode .btn-outline-info:hover { background-color: var(--bs-info); color: #000; } /* Info often light, text dark */


/* Inputs & Selects */
.form-control, .form-select {
  border-radius: 0.375rem;
  background-color: #fff;
  color: #212529;
  border: 1px solid #ced4da;
  transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out, background-color .3s, color .3s;
}

.form-control-sm, .form-select-sm {
  font-size: 0.875rem; /* Standard BS5 sm form font size */
}

/* Dark Mode Form Elements (Specific styles from charts.html <style> block are good, this is for general consistency) */
.dark-mode .form-control, .dark-mode .form-select {
    background-color: #2a2a2a;
    color: #e0e0e0;
    border-color: #444;
}
.dark-mode .form-control::placeholder { color: #888; }
.dark-mode .form-control:focus, .dark-mode .form-select:focus {
    background-color: #333;
    color: #fff;
    border-color: var(--bs-primary); /* Use theme color for focus */
    box-shadow: 0 0 0 0.25rem rgba(var(--bs-primary-rgb), 0.25);
}
.dark-mode input[type="date"]::-webkit-calendar-picker-indicator {
  filter: invert(0.8); /* Make date picker icon visible in dark mode */
}


/* Chart container */
.chart-container {
  position: relative;
  /* height: 400px; (set inline in HTML for specific needs) */
  width: 100%;
}

/* Summary Table (for chart data) & Raw Records Table */
#summaryTable table, #rawRecordsTableContainer table {
  width: 100%;
  font-size: 0.875rem; /* Slightly smaller for tables */
}
#summaryTable th, #summaryTable td,
#rawRecordsTableContainer th, #rawRecordsTableContainer td {
  padding: 0.5rem; /* Adjust padding for density */
  text-align: left;
  vertical-align: middle;
}
#rawRecordsTableContainer th {
    background-color: #f8f9fa; /* Light header for table */
}
.dark-mode #rawRecordsTableContainer th {
    background-color: #2c2c2c; /* Dark header for table */
}
.dark-mode .table-dark { /* Ensure Bootstrap's table-dark is applied correctly */
    color: #e0e0e0;
    border-color: #32383e;
}
.dark-mode .table-striped > tbody > tr:nth-of-type(odd) > * {
    --bs-table-accent-bg: rgba(255, 255, 255, 0.03); /* Subtler stripe for dark mode */
}


/* Snapshot Images */
.snapshot {
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border-radius: 0.25rem; /* Bootstrap default for thumbnails */
  border: 1px solid #dee2e6;
}
.snapshot:hover {
  transform: scale(1.1); /* Slightly larger hover effect */
  box-shadow: 0 0.25rem 0.75rem rgba(0,0,0,0.15);
}
.dark-mode .snapshot {
  border-color: #444;
}

/* Modal */
.modal-content {
  border-radius: 0.5rem;
  transition: background-color 0.3s, color 0.3s;
}
.dark-mode .modal-content {
  background-color: #1e1e1e;
  color: #e0e0e0;
}
.dark-mode .modal-header .btn-close {
    filter: invert(1) grayscale(100%) brightness(200%); /* Make close button white */
}

/* Filter Chips (already in charts.html style block, ensure consistency or merge) */
.filter-chip {
    display: inline-block;
    padding: 0.3em 0.6em;
    font-size: 0.85em;
    margin-right: 6px;
    margin-bottom: 6px;
    background-color: #e9ecef;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    color: #495057;
}
.dark-mode .filter-chip {
    background-color: #3a3a3a;
    color: #ccc;
    border: 1px solid #555;
}

/* KPI Card Icons */
#kpiCards .card h5 i {
    margin-right: 0.5rem;
}


/* Responsive Layout */
@media screen and (max-width: 768px) {
  body { font-size: 15px; }
  .btn, .form-select-sm, .form-control-sm {
    font-size: 0.8rem; /* Smaller fonts for controls on mobile */
    padding: 0.4rem 0.8rem;
  }
  .chart-container {
    min-height: 300px !important; /* Ensure chart is visible */
  }
  .filter-chip {
    font-size: 0.75em;
    padding: 0.25em 0.5em;
  }
  #kpiCards .col-md { /* Make KPI cards stack nicely */
    flex-basis: auto; /* Allow them to take full width if needed */
  }
  .log-month .card-body, .log-date .card-body {
      padding-left: 0.5rem; /* Reduce padding on mobile for nested cards */
      padding-right: 0.5rem;
  }
  .log-date {
      margin-left: 0.5rem !important; /* Reduce indent on mobile */
  }
}

/* Specific for range slider in charts.html */
.range-slider-group .form-range {
    padding: 0 0.5rem; /* Add some padding around the slider thumb track */
}
.range-slider-group .input-group-text {
    font-size: 0.8em;
    padding: 0.375rem 0.5rem;
}
.dark-mode .range-slider-group .input-group-text {
    background-color: #3a3a3a;
    border-color: #444;
    color: #ccc;
}

/* Table scrollbar styling (optional, webkit only) */
.table-responsive::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}
.table-responsive::-webkit-scrollbar-track {
  background: transparent; /* Or match card bg */
}
.table-responsive::-webkit-scrollbar-thumb {
  background-color: #adb5bd; /* Neutral scrollbar color */
  border-radius: 4px;
  border: 2px solid transparent; /* Creates padding around thumb */
  background-clip: content-box;
}
.dark-mode .table-responsive::-webkit-scrollbar-thumb {
  background-color: #495057;
}

/* Enhanced KPI Cards with theme-specific hover effects */
#kpiCards .card {
  transition: transform 0.2s ease-in-out, box-shadow 0.3s ease, background-color 0.3s;
  border: none;
  overflow: hidden;
  height: 100%;
}

#kpiCards .card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  z-index: 1;
}

.dark-mode #kpiCards .card:hover {
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
  background-color: #2a2a2a;
}

.blue-mode #kpiCards .card:hover {
  box-shadow: 0 10px 20px rgba(10, 25, 47, 0.3);
  background-color: #233554;
}

.purple-mode #kpiCards .card:hover {
  box-shadow: 0 10px 20px rgba(19, 17, 28, 0.3);
  background-color: #2d2a3d;
}

/* Enhanced Filter Buttons */
.btn-outline-primary, .btn-outline-secondary, .btn-outline-success, 
.btn-outline-info, .btn-outline-dark, .btn-outline-warning {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  z-index: 1;
}

.btn-outline-primary:hover, .btn-outline-secondary:hover, .btn-outline-success:hover,
.btn-outline-info:hover, .btn-outline-dark:hover, .btn-outline-warning:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.dark-mode .btn-outline-primary:hover, .dark-mode .btn-outline-secondary:hover, 
.dark-mode .btn-outline-success:hover, .dark-mode .btn-outline-info:hover, 
.dark-mode .btn-outline-dark:hover, .dark-mode .btn-outline-warning:hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

/* Enhanced Filter Chips */
.filter-chip {
  transition: all 0.2s ease;
  cursor: pointer;
  border-radius: 16px;
  padding: 0.4em 0.8em;
}

.filter-chip:hover {
  transform: translateY(-2px);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
}

.dark-mode .filter-chip:hover {
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
  background-color: #444;
}

/* Enhanced Form Controls */
.form-control, .form-select {
  transition: border-color 0.2s ease, box-shadow 0.2s ease, transform 0.2s ease;
}

.form-control:focus, .form-select:focus {
  transform: translateY(-1px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05), 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.25);
}

.dark-mode .form-control:focus, .dark-mode .form-select:focus {
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2), 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.25);
}

/* Enhanced Log Headers */
.log-month-header, .log-date-header {
  transition: background-color 0.3s, color 0.3s, transform 0.2s;
}

.log-month-header:hover, .log-date-header:hover {
  background-color: #dee2e6;
  transform: translateY(-1px);
}

.dark-mode .log-month-header:hover, .dark-mode .log-date-header:hover {
  background-color: #3a3a3a;
}

/* Enhanced Table Rows */
.table tbody tr {
  transition: background-color 0.2s, transform 0.2s;
}

.table tbody tr:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 1;
}

.dark-mode .table tbody tr:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

