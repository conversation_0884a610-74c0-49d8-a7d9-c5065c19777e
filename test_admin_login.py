#!/usr/bin/env python3
"""
Script to test admin login with common passwords
"""

import os
import sys
import hashlib
from dotenv import load_dotenv

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
load_dotenv()

# Import auth module
from auth import authenticate_user

def test_admin_passwords():
    """Test common passwords for admin user"""
    email = "<EMAIL>"
    
    # Common passwords to try
    passwords = [
        "admin",
        "password",
        "admin123",
        "password123",
        "tutordashboard",
        "<EMAIL>",
        "dashboard",
        "tutor",
        "123456",
        "admin2024",
        "stemcenter",
        "gannon"
    ]
    
    print(f"Testing login for {email}...")
    
    for password in passwords:
        print(f"Trying password: {password}")
        success, message = authenticate_user(email, password)
        
        if success:
            print(f"✅ SUCCESS! Password is: {password}")
            return password
        else:
            print(f"❌ Failed: {message}")
    
    print("❌ None of the common passwords worked")
    
    # Let's also check what the hash should be for some common passwords
    print("\nChecking hash values for common passwords:")
    stored_hash = "240be518fabd2724ddb6f04eeb1da5967448d7e831c08c8fa822809f74c720a9"
    
    for password in passwords[:5]:  # Check first 5
        simple_hash = hashlib.sha256(password.encode()).hexdigest()
        print(f"Password '{password}' -> Hash: {simple_hash}")
        if simple_hash == stored_hash:
            print(f"🎯 MATCH! The password is: {password}")
            return password
    
    return None

if __name__ == "__main__":
    test_admin_passwords()
