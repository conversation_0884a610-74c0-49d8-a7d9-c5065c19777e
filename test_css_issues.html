<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSS Test Page</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="/static/style.css">
    <style>
        /* Test styles to identify issues */
        .test-container {
            padding: 2rem;
            background: #f8f9fa;
        }
        
        .test-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem;
            border-radius: 8px;
        }
        
        .test-text {
            color: #333;
            font-size: 1rem;
        }
        
        .dark-mode .test-text {
            color: #e0e0e0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>CSS Test Page</h1>
        
        <div class="test-card">
            <h3>Basic Card Test</h3>
            <p class="test-text">This is a test paragraph to check text visibility.</p>
            <div class="test-gradient">
                <p>Gradient background test</p>
            </div>
        </div>
        
        <div class="test-card">
            <h3>Bootstrap Components Test</h3>
            <button class="btn btn-primary">Primary Button</button>
            <button class="btn btn-success">Success Button</button>
            <div class="alert alert-info mt-3">
                <i class="fas fa-info-circle"></i> This is an info alert
            </div>
        </div>
        
        <div class="test-card">
            <h3>Form Elements Test</h3>
            <div class="mb-3">
                <label for="testInput" class="form-label">Test Input</label>
                <input type="text" class="form-control" id="testInput" placeholder="Enter text">
            </div>
            <div class="mb-3">
                <label for="testSelect" class="form-label">Test Select</label>
                <select class="form-select" id="testSelect">
                    <option>Option 1</option>
                    <option>Option 2</option>
                </select>
            </div>
        </div>
        
        <div class="test-card">
            <h3>Dark Mode Toggle Test</h3>
            <button onclick="toggleDarkMode()" class="btn btn-outline-secondary">
                <i class="fas fa-moon"></i> Toggle Dark Mode
            </button>
        </div>
    </div>
    
    <script>
        function toggleDarkMode() {
            document.body.classList.toggle('dark-mode');
            document.documentElement.classList.toggle('dark-mode');
        }
        
        // Check for CSS loading errors
        window.addEventListener('load', function() {
            console.log('🔍 Checking CSS loading...');
            
            // Check if Bootstrap is loaded
            const bootstrapTest = window.getComputedStyle(document.querySelector('.btn')).display;
            console.log('Bootstrap loaded:', bootstrapTest !== 'inline');
            
            // Check if Font Awesome is loaded
            const faTest = window.getComputedStyle(document.querySelector('.fas')).fontFamily;
            console.log('Font Awesome loaded:', faTest.includes('Font Awesome'));
            
            // Check if custom CSS is loaded
            const customTest = window.getComputedStyle(document.querySelector('.test-container')).padding;
            console.log('Custom CSS loaded:', customTest === '32px');
            
            // Check for CSS errors
            const stylesheets = document.styleSheets;
            for (let i = 0; i < stylesheets.length; i++) {
                try {
                    const rules = stylesheets[i].cssRules || stylesheets[i].rules;
                    console.log(`Stylesheet ${i} loaded successfully with ${rules.length} rules`);
                } catch (e) {
                    console.error(`Error loading stylesheet ${i}:`, e);
                }
            }
        });
    </script>
</body>
</html>
