#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to create a test user for the dashboard
"""

import os
import sys
from dotenv import load_dotenv

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
load_dotenv()

# Import auth module
from auth import register_user

def create_test_user():
    """Create a test user for development"""
    print("Creating test user...")
    
    # Test user credentials
    email = "<EMAIL>"
    password = "test12345"  # At least 8 characters
    role = "manager"  # Manager role to access all features
    tutor_id = "12345"
    full_name = "Test User"
    
    try:
        success, message = register_user(email, password, role, tutor_id, full_name)
        
        if success:
            print(f"✅ Success: {message}")
            print(f"📧 Email: {email}")
            print(f"🔑 Password: {password}")
            print(f"👤 Role: {role}")
            print(f"🆔 Tutor ID: {tutor_id}")
        else:
            print(f"❌ Failed: {message}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    create_test_user()
