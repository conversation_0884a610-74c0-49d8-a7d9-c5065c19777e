# Single vs Comparison Mode Analysis

## Overview

The application supports two analysis modes:
1. **Single Mode**: Default mode that shows aggregated data
2. **Comparison Mode**: Shows individual tutor data side-by-side for comparison

## Mode Determination Logic

### Backend Logic
```python
analysis_mode = filters.get('analysisMode', 'single')
is_comparison_mode = (analysis_mode == 'comparison' and len(selected_tutor_ids) > 1) or len(selected_tutor_ids) > 1
```

**Comparison mode is activated when:**
- User explicitly selects "Compare" mode AND has 2+ tutors selected, OR
- User has 2+ tutors selected (automatic comparison)

### Frontend Logic
```javascript
// Analysis mode toggle
const analysisMode = document.querySelector('input[name="analysisMode"]:checked');
if (analysisMode) params.append('analysisMode', analysisMode.value);
```

## Data Processing Differences

### Single Mode
**Aggregated Data Approach:**
- All selected tutors' data is combined into single aggregated values
- Shows totals, averages, and combined patterns
- One data series per chart

**Example Data Structure:**
```json
{
  "daily_checkins": {
    "2025-01-15": 5,
    "2025-01-16": 8,
    "2025-01-17": 3
  },
  "checkins_per_tutor": {
    "John Doe": 12,
    "Jane Smith": 8,
    "Bob Wilson": 6
  }
}
```

### Comparison Mode
**Individual Tutor Approach:**
- Each tutor's data is processed separately
- Shows individual patterns and trends
- Multiple data series per chart (one per tutor)

**Example Data Structure:**
```json
{
  "daily_checkins": {
    "John Doe": {
      "2025-01-15": 2,
      "2025-01-16": 3,
      "2025-01-17": 1
    },
    "Jane Smith": {
      "2025-01-15": 3,
      "2025-01-16": 5,
      "2025-01-17": 2
    }
  }
}
```

## Chart Type Behavior

### Charts That Support Comparison Mode

#### 1. `daily_checkins`
- **Single Mode**: Shows total daily check-ins across all selected tutors
- **Comparison Mode**: Shows separate line/bar for each tutor's daily check-ins
- **Use Case**: Compare daily activity patterns between tutors

#### 2. `daily_hours`
- **Single Mode**: Shows total daily hours across all selected tutors
- **Comparison Mode**: Shows separate line/bar for each tutor's daily hours
- **Use Case**: Compare work patterns and productivity between tutors

### Charts That Filter in Comparison Mode

#### 3. `checkins_per_tutor`
- **Single Mode**: Shows all tutors (or filtered tutors)
- **Comparison Mode**: Shows only selected tutors
- **Use Case**: Focus on specific tutors for detailed analysis

#### 4. `hours_per_tutor`
- **Single Mode**: Shows all tutors (or filtered tutors)
- **Comparison Mode**: Shows only selected tutors
- **Use Case**: Compare total hours between specific tutors

### Charts That Aggregate in Both Modes

#### 5. `cumulative_checkins` / `cumulative_hours`
- **Single Mode**: Cumulative totals across all selected tutors
- **Comparison Mode**: Still shows aggregated cumulative (not per-tutor)
- **Behavior**: Same in both modes (aggregated)

#### 6. `hourly_checkins_dist` / `monthly_hours`
- **Single Mode**: Distribution across all selected tutors
- **Comparison Mode**: Distribution across selected tutors only
- **Behavior**: Filtered but still aggregated

#### 7. `avg_hours_per_day_of_week` / `checkins_per_day_of_week`
- **Single Mode**: Patterns across all selected tutors
- **Comparison Mode**: Patterns across selected tutors only
- **Behavior**: Filtered but still aggregated

#### 8. `hourly_activity_by_day`
- **Single Mode**: Heatmap across all selected tutors
- **Comparison Mode**: Heatmap across selected tutors only
- **Behavior**: Filtered but still aggregated

## Backend Implementation Details

### Single Mode Processing
```python
# Standard aggregation
daily_checkins_series = df_filtered.groupby(df_filtered['check_in'].dt.date).size()
daily_checkins = {date_obj.strftime('%Y-%m-%d'): count for date_obj, count in daily_checkins_series.items()}
```

### Comparison Mode Processing
```python
# Individual tutor processing
daily_checkins_comparison = {}
for tutor_id_comp in selected_tutor_ids:
    df_tutor_base = df_orig[df_orig['tutor_id'].astype(str) == tutor_id_comp]
    df_tutor = apply_filters(df_tutor_base, tutor_filters)
    
    tutor_name = df_tutor['tutor_name'].iloc[0]
    t_daily_checkins_series = df_tutor.groupby(df_tutor['check_in'].dt.date).size()
    daily_checkins_comparison[tutor_name] = {date_obj.strftime('%Y-%m-%d'): count for date_obj, count in t_daily_checkins_series.items()}
```

### Data Selection Logic
```python
# Choose appropriate data based on mode and chart type
final_daily_checkins = daily_checkins_comparison if is_comparison_mode and chart_key_requested == 'daily_checkins' and daily_checkins_comparison else daily_checkins
```

## Frontend Rendering Differences

### Single Mode Rendering
```javascript
// Single dataset
chartConfig.data = {
  labels: Object.keys(data),
  datasets: [{
    label: title,
    data: Object.values(data),
    backgroundColor: getColor(0, 0.7),
    borderColor: getColor(0, 1)
  }]
};
```

### Comparison Mode Rendering
```javascript
// Multiple datasets (one per tutor)
if (isComparison && (typeof data === 'object' && !Array.isArray(data))) {
  const tutorNames = Object.keys(data);
  const allDates = [...new Set(tutorNames.flatMap(name => Object.keys(data[name])))].sort();
  
  chartConfig.data = {
    labels: allDates,
    datasets: tutorNames.map((name, index) => ({
      label: name,
      data: allDates.map(date => data[name][date] || 0),
      backgroundColor: getColor(index, 0.7),
      borderColor: getColor(index, 1)
    }))
  };
}
```

## User Interface Differences

### Single Mode UI
- **Analysis Mode**: "Single" radio button selected
- **Chart Display**: Single color scheme, one legend entry
- **Filter Chips**: Shows selected tutors but data is aggregated
- **Interpretation**: "Total across selected tutors"

### Comparison Mode UI
- **Analysis Mode**: "Compare" radio button selected
- **Chart Display**: Multiple colors, multiple legend entries
- **Filter Chips**: Shows "Mode: Comparison" + selected tutors
- **Interpretation**: "Individual tutor breakdown"

## Use Cases

### When to Use Single Mode
1. **Overall Performance**: Want to see total/average performance
2. **Trend Analysis**: Interested in overall trends across tutors
3. **Capacity Planning**: Need aggregate numbers for planning
4. **Summary Reports**: Creating high-level summaries

### When to Use Comparison Mode
1. **Performance Comparison**: Compare individual tutor performance
2. **Pattern Analysis**: Identify different working patterns
3. **Outlier Detection**: Spot tutors with unusual patterns
4. **Individual Assessment**: Evaluate specific tutors

## Advanced Filter Integration

### Single Mode + Advanced Filters
- Filters applied to combined dataset
- Results show aggregated filtered data
- Example: "Total hours for selected tutors with >2 hour sessions"

### Comparison Mode + Advanced Filters
- Filters applied to each tutor individually
- Results show per-tutor filtered data
- Example: "Each tutor's hours from >2 hour sessions"

## Performance Considerations

### Single Mode
- **Faster**: Single aggregation operation
- **Less Memory**: One dataset per chart type
- **Simpler**: Straightforward data processing

### Comparison Mode
- **Slower**: Multiple individual processing operations
- **More Memory**: Multiple datasets per chart type
- **Complex**: Requires individual filter application

## Validation and Error Handling

### Mode Validation
```javascript
// Prevent comparison mode with insufficient tutors
if (this.value === 'comparison' && selectedTutors.length < 2) {
  showToast('Please select at least 2 tutors for comparison mode', 'warning');
  document.getElementById('singleMode').checked = true;
  comparisonMode = false;
  return;
}
```

### Data Validation
- **Single Mode**: Validates aggregated data exists
- **Comparison Mode**: Validates each tutor has data
- **Fallback**: Falls back to single mode if comparison data unavailable

## Summary

| Aspect | Single Mode | Comparison Mode |
|--------|-------------|-----------------|
| **Data Structure** | Aggregated values | Per-tutor breakdown |
| **Chart Series** | Single series | Multiple series (one per tutor) |
| **Use Case** | Overall analysis | Individual comparison |
| **Performance** | Faster | Slower |
| **Complexity** | Simple | Complex |
| **Filter Application** | Combined dataset | Individual datasets |
| **Visual Output** | Single color/line | Multiple colors/lines |
| **Best For** | Totals, trends, summaries | Comparisons, patterns, outliers |

The key difference is that **Single Mode aggregates data across tutors** while **Comparison Mode preserves individual tutor identity** in the data and visualization.