#!/usr/bin/env python3
"""
Script to check the user table schema
"""

import os
import sys
from dotenv import load_dotenv

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
load_dotenv()

# Import auth module
from auth import supabase

def check_user_schema():
    """Check the schema of the users table"""
    print("Checking users table schema...")
    
    try:
        # Get the existing user to see what columns are available
        response = supabase.table('users').select('*').limit(1).execute()
        
        if response.data and len(response.data) > 0:
            user = response.data[0]
            print("Available columns in users table:")
            for key, value in user.items():
                print(f"  - {key}: {type(value).__name__} = {value}")
        else:
            print("No users found to check schema")
            
    except Exception as e:
        print(f"Error checking users table: {e}")

if __name__ == "__main__":
    check_user_schema()
