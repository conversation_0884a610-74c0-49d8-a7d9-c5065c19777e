# 📋 Tutor Face Recognition Dashboard

This is a Flask-based web application built for Gannon University's STEM Center. It allows staff to manage tutor attendance using face recognition and provides rich analytics through charts and filters.

---

## 🔧 Features

- 📅 Logs grouped by date with collapsible sections
- 🧑 Manual check-in/check-out system with snapshot tracking
- 📊 Interactive analytics dashboard with chart export
- 🌙 Toggle between dark and light mode
- 🔍 Filter logs by Tu<PERSON> ID and date range
- 📸 Snapshot viewer modal for check-in/out verification
- 🔁 Expand/Collapse all sections at once
- ⬇️ Download attendance logs in CSV format

---

## 🚀 How to Run Locally

## Setup Instructions

1. Clone the repository:
   ```
   git clone <repo-url>
   cd stem-face-dashboard-main
   ```
2. Install dependencies:
   ```
   pip install -r requirements.txt
   ```
3. Set up environment variables:
   - Create a `.env` file in the root directory.
   - Add any required variables (see below).

## Environment Variables
- `SUPABASE_URL` - Your Supabase project URL
- `SUPABASE_KEY` - Your Supabase API key
- `SECRET_KEY` - Flask secret key
- (Add others as needed)

## Running the App
```
python app.py
```

## Troubleshooting
- Ensure all dependencies are installed.
- Check your `.env` file for required variables.
- Review logs for errors (see logs/ directory).
- For deployment, ensure your Procfile is correct.

## Contributing
Pull requests are welcome. For major changes, please open an issue first to discuss what you would like to change.

```bash
# Clone the repository
git clone https://github.com/AvishManiar21/stem-face-dashboard.git
cd stem-face-dashboard

# Install dependencies
pip install -r requirements.txt

# Run the Flask app
python app.py
