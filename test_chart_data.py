#!/usr/bin/env python3
"""
Test script to verify chart data processing logic
"""

import pandas as pd
import sys
import os

# Add the current directory to Python path to import app modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_chart_data_processing():
    """Test the chart data processing logic directly"""
    
    print("🧪 Testing Chart Data Processing Logic")
    print("=" * 50)
    
    try:
        # Import the load_data function from app
        from app import load_data
        
        # Load the data
        print("📊 Loading data from CSV...")
        df = load_data()
        
        if df.empty:
            print("❌ No data loaded from CSV file")
            return
        
        print(f"✅ Data loaded successfully: {len(df)} records")
        print(f"📋 Columns: {df.columns.tolist()}")
        
        # Test data processing for different chart types
        test_datasets = {
            'checkins_per_tutor': 'tutor_name',
            'hours_per_tutor': 'tutor_name',
            'daily_checkins': 'check_in',
            'daily_hours': 'check_in',
            'hourly_checkins_dist': 'hour',
            'monthly_hours': 'month_year',
            'avg_hours_per_day_of_week': 'day_name',
            'checkins_per_day_of_week': 'day_name'
        }
        
        print("\n📈 Testing dataset calculations:")
        
        for dataset_name, required_column in test_datasets.items():
            print(f"\n🔍 Testing {dataset_name}...")
            
            if required_column not in df.columns:
                print(f"   ❌ Missing required column: {required_column}")
                continue
            
            try:
                if dataset_name == 'checkins_per_tutor':
                    result = df['tutor_name'].value_counts().to_dict()
                elif dataset_name == 'hours_per_tutor':
                    result = df.groupby('tutor_name')['shift_hours'].sum().round(2).to_dict()
                elif dataset_name == 'daily_checkins':
                    result = df.groupby(df['check_in'].dt.date).size().to_dict()
                elif dataset_name == 'daily_hours':
                    result = df.groupby(df['check_in'].dt.date)['shift_hours'].sum().round(2).to_dict()
                elif dataset_name == 'hourly_checkins_dist':
                    result = df.groupby('hour').size().to_dict()
                elif dataset_name == 'monthly_hours':
                    result = df.groupby('month_year')['shift_hours'].sum().round(2).to_dict()
                elif dataset_name == 'avg_hours_per_day_of_week':
                    result = df.groupby('day_name')['shift_hours'].mean().round(2).to_dict()
                elif dataset_name == 'checkins_per_day_of_week':
                    result = df['day_name'].value_counts().to_dict()
                
                if result:
                    print(f"   ✅ Success: {len(result)} data points")
                    # Show first few items
                    items = list(result.items())[:3]
                    for key, value in items:
                        print(f"      {key}: {value}")
                else:
                    print(f"   ⚠️  Warning: No data generated")
                    
            except Exception as e:
                print(f"   ❌ Error: {e}")
        
        # Test new datasets
        print("\n🆕 Testing new datasets:")
        
        # Average session duration per tutor
        try:
            avg_duration = df.groupby('tutor_name')['shift_hours'].mean().round(2)
            print(f"   ✅ avg_session_duration_per_tutor: {len(avg_duration)} tutors")
        except Exception as e:
            print(f"   ❌ avg_session_duration_per_tutor error: {e}")
        
        # Tutor consistency score
        try:
            consistency_stats = df.groupby('tutor_name')['shift_hours'].agg(['mean', 'std', 'count']).reset_index()
            consistency_stats = consistency_stats[consistency_stats['count'] >= 2]
            print(f"   ✅ tutor_consistency_score: {len(consistency_stats)} tutors with 2+ sessions")
        except Exception as e:
            print(f"   ❌ tutor_consistency_score error: {e}")
        
        # Punctuality analysis
        try:
            punctuality = {
                'Early (>15min early)': len(df[df['check_in'].dt.hour < 9]),
                'On Time (±15min)': len(df[(df['check_in'].dt.hour >= 9) & (df['check_in'].dt.hour <= 17)]),
                'Late (>15min late)': len(df[df['check_in'].dt.hour > 17])
            }
            print(f"   ✅ punctuality_analysis: {sum(punctuality.values())} total sessions")
        except Exception as e:
            print(f"   ❌ punctuality_analysis error: {e}")
        
        # Session duration distribution
        try:
            duration_dist = {
                'Short (<1h)': len(df[df['shift_hours'] < 1]),
                'Medium (1-3h)': len(df[(df['shift_hours'] >= 1) & (df['shift_hours'] < 3)]),
                'Long (3-6h)': len(df[(df['shift_hours'] >= 3) & (df['shift_hours'] < 6)]),
                'Extended (6h+)': len(df[df['shift_hours'] >= 6])
            }
            print(f"   ✅ session_duration_distribution: {sum(duration_dist.values())} total sessions")
        except Exception as e:
            print(f"   ❌ session_duration_distribution error: {e}")
        
        print("\n" + "=" * 50)
        print("🏁 Chart data processing test complete!")
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure you're running this from the project directory")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    test_chart_data_processing() 