#!/usr/bin/env python3
"""
Script to test dashboard access after login
"""

import requests
import json

def test_dashboard_access():
    """Test dashboard access with admin credentials"""
    base_url = "http://127.0.0.1:5000"
    
    # Create a session to maintain cookies
    session = requests.Session()
    
    # Login credentials
    login_data = {
        'email': '<EMAIL>',
        'password': 'admin123'
    }
    
    print("🔐 Attempting to login...")
    
    # Attempt login
    login_response = session.post(f"{base_url}/login", data=login_data)
    
    if login_response.status_code == 200:
        print("✅ Login successful!")
        
        # Test dashboard access
        print("📊 Testing dashboard access...")
        dashboard_response = session.get(f"{base_url}/")
        
        if dashboard_response.status_code == 200:
            print("✅ Dashboard accessible!")
            
            # Test dashboard data endpoint
            print("📈 Testing dashboard data endpoint...")
            dashboard_data_response = session.get(f"{base_url}/dashboard-data")
            
            if dashboard_data_response.status_code == 200:
                print("✅ Dashboard data endpoint working!")
                data = dashboard_data_response.json()
                print(f"📊 Summary data: {data.get('summary', {})}")
                
                # Test chart data endpoint
                print("📊 Testing chart data endpoint...")
                chart_data_response = session.post(
                    f"{base_url}/chart-data",
                    headers={'Content-Type': 'application/json'},
                    data=json.dumps({'chartKey': 'daily_hours'})
                )
                
                if chart_data_response.status_code == 200:
                    print("✅ Chart data endpoint working!")
                    chart_data = chart_data_response.json()
                    print(f"📊 Chart data keys: {list(chart_data.keys())}")
                else:
                    print(f"❌ Chart data endpoint failed: {chart_data_response.status_code}")
                    print(chart_data_response.text)
                
            else:
                print(f"❌ Dashboard data endpoint failed: {dashboard_data_response.status_code}")
                print(dashboard_data_response.text)
        else:
            print(f"❌ Dashboard access failed: {dashboard_response.status_code}")
            print(dashboard_response.text)
    else:
        print(f"❌ Login failed: {login_response.status_code}")
        print(login_response.text)

if __name__ == "__main__":
    test_dashboard_access()
