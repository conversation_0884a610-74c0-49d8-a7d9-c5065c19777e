<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📋 Audit Logs - Tutor Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="/static/style.css">
    <style>
        .audit-table-container {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border-radius: 0.375rem;
        }
        
        .audit-table th {
            font-weight: 600;
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }
        
        .audit-table td {
            vertical-align: middle;
            font-size: 0.875rem;
        }
        
        .audit-table tbody tr:hover {
            background-color: rgba(0, 123, 255, 0.05);
        }
        
        .badge {
            font-size: 0.75rem;
            font-weight: 500;
        }
        
        .pagination-container {
            background-color: #f8f9fa;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-top: 1rem;
        }
        
        .page-link {
            border-radius: 0.25rem;
            margin: 0 0.125rem;
            border: 1px solid #dee2e6;
        }
        
        .page-link:hover {
            background-color: #e9ecef;
            border-color: #adb5bd;
        }
        
        .page-item.active .page-link {
            background-color: #0d6efd;
            border-color: #0d6efd;
        }
        
        .entries-info {
            font-size: 0.875rem;
            color: #6c757d;
        }
        
        .per-page-selector {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .timestamp-cell {
            min-width: 140px;
        }
        
        .action-cell {
            min-width: 120px;
        }
        
        .user-cell {
            min-width: 180px;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-user-graduate"></i> Tutor Dashboard
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('index') }}">
                    <i class="fas fa-tachometer-alt"></i> Dashboard
                </a>
                <a class="nav-link" href="{{ url_for('charts_page') }}">
                    <i class="fas fa-chart-bar"></i> Charts
                </a>
                <a class="nav-link" href="{{ url_for('admin_users') }}">
                    <i class="fas fa-users-cog"></i> Users
                </a>
                <a class="nav-link active" href="{{ url_for('admin_audit_logs') }}">
                    <i class="fas fa-clipboard-list"></i> Audit Logs
                </a>
                <a class="nav-link" href="{{ url_for('logout') }}">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1><i class="fas fa-clipboard-list"></i> Admin Audit Logs</h1>
                    <div>
                        <button class="btn btn-outline-primary" onclick="refreshLogs()">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                        <form method="POST" action="{{ url_for('admin_populate_audit_logs') }}" style="display: inline;">
                            <button type="submit" class="btn btn-outline-info">
                                <i class="fas fa-database"></i> Populate from Face Data
                            </button>
                        </form>
                        <button class="btn btn-outline-success" onclick="exportLogs()">
                            <i class="fas fa-download"></i> Export CSV
                        </button>
                    </div>
                </div>

                <!-- Audit Logs Table -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-history"></i> Admin Actions History</h5>
                        {% if pagination and pagination.total > 0 %}
                            <small class="text-muted">
                                Showing {{ pagination.start_entry }}-{{ pagination.end_entry }} of {{ pagination.total }} entries
                            </small>
                        {% endif %}
                    </div>
                    <div class="card-body">
                        {% if logs %}
                            <div class="table-responsive audit-table-container">
                                <table class="table table-striped table-hover audit-table" id="auditTable">
                                    <thead class="table-dark">
                                        <tr>
                                            <th class="timestamp-cell">Timestamp</th>
                                            <th class="user-cell">Admin User</th>
                                            <th class="action-cell">Action</th>
                                            <th>Target User</th>
                                            <th>Details</th>
                                            <th>IP Address</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for log in logs %}
                                            <tr>
                                                <td>
                                                    <small class="text-muted">
                                                        {{ log.timestamp if log.timestamp else '-' }}
                                                    </small>
                                                </td>
                                                <td>
                                                    <strong>{{ log.admin_email or 'Unknown' }}</strong>
                                                    <br><small class="text-muted">{{ (log.admin_user_id[:8] + '...') if log.admin_user_id and log.admin_user_id|length > 8 else (log.admin_user_id or '-') }}</small>
                                                </td>
                                                <td>
                                                    {% set badge_class = 'info' %}
                                                    {% if log.action == 'CREATE_USER' %}
                                                        {% set badge_class = 'success' %}
                                                    {% elif log.action == 'UPDATE_USER_ROLE' %}
                                                        {% set badge_class = 'warning' %}
                                                    {% elif log.action == 'DELETE_USER' %}
                                                        {% set badge_class = 'danger' %}
                                                    {% elif log.action == 'TUTOR_CHECK_IN' %}
                                                        {% set badge_class = 'primary' %}
                                                    {% elif log.action == 'TUTOR_CHECK_OUT' %}
                                                        {% set badge_class = 'secondary' %}
                                                    {% endif %}
                                                    <span class="badge bg-{{ badge_class }}">
                                                        {{ log.action.replace('_', ' ').title() }}
                                                    </span>
                                                </td>
                                                <td>{{ log.target_user_email or '-' }}</td>
                                                <td>
                                                    <small>{{ log.details or '-' }}</small>
                                                </td>
                                                <td>
                                                    <small class="text-muted">{{ log.ip_address or '-' }}</small>
                                                </td>
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>

                            <!-- Pagination Controls -->
                            {% if pagination and pagination.total_pages > 1 %}
                                <div class="pagination-container">
                                    <nav aria-label="Audit logs pagination">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div class="per-page-selector">
                                                <label for="perPageSelect" class="form-label me-2 mb-0">Show:</label>
                                                <select id="perPageSelect" class="form-select form-select-sm" style="width: auto;" onchange="changePerPage(this.value)">
                                                    <option value="10" {{ 'selected' if pagination.per_page == 10 else '' }}>10</option>
                                                    <option value="20" {{ 'selected' if pagination.per_page == 20 else '' }}>20</option>
                                                    <option value="50" {{ 'selected' if pagination.per_page == 50 else '' }}>50</option>
                                                    <option value="100" {{ 'selected' if pagination.per_page == 100 else '' }}>100</option>
                                                </select>
                                                <span class="ms-2 text-muted">entries per page</span>
                                            </div>
                                        
                                        <ul class="pagination pagination-sm mb-0">
                                            <!-- First Page -->
                                            <li class="page-item {{ 'disabled' if not pagination.has_prev else '' }}">
                                                <a class="page-link" href="{{ url_for('admin_audit_logs', page=1, per_page=pagination.per_page) if pagination.has_prev else '#' }}">
                                                    <i class="fas fa-angle-double-left"></i>
                                                </a>
                                            </li>
                                            
                                            <!-- Previous Page -->
                                            <li class="page-item {{ 'disabled' if not pagination.has_prev else '' }}">
                                                <a class="page-link" href="{{ url_for('admin_audit_logs', page=pagination.prev_num, per_page=pagination.per_page) if pagination.has_prev else '#' }}">
                                                    <i class="fas fa-angle-left"></i>
                                                </a>
                                            </li>
                                            
                                            <!-- Page Numbers -->
                                            {% set start_page = [1, pagination.page - 2]|max %}
                                            {% set end_page = [pagination.total_pages, pagination.page + 2]|min %}
                                            
                                            {% if start_page > 1 %}
                                                <li class="page-item">
                                                    <a class="page-link" href="{{ url_for('admin_audit_logs', page=1, per_page=pagination.per_page) }}">1</a>
                                                </li>
                                                {% if start_page > 2 %}
                                                    <li class="page-item disabled">
                                                        <span class="page-link">...</span>
                                                    </li>
                                                {% endif %}
                                            {% endif %}
                                            
                                            {% for page_num in range(start_page, end_page + 1) %}
                                                <li class="page-item {{ 'active' if page_num == pagination.page else '' }}">
                                                    <a class="page-link" href="{{ url_for('admin_audit_logs', page=page_num, per_page=pagination.per_page) }}">{{ page_num }}</a>
                                                </li>
                                            {% endfor %}
                                            
                                            {% if end_page < pagination.total_pages %}
                                                {% if end_page < pagination.total_pages - 1 %}
                                                    <li class="page-item disabled">
                                                        <span class="page-link">...</span>
                                                    </li>
                                                {% endif %}
                                                <li class="page-item">
                                                    <a class="page-link" href="{{ url_for('admin_audit_logs', page=pagination.total_pages, per_page=pagination.per_page) }}">{{ pagination.total_pages }}</a>
                                                </li>
                                            {% endif %}
                                            
                                            <!-- Next Page -->
                                            <li class="page-item {{ 'disabled' if not pagination.has_next else '' }}">
                                                <a class="page-link" href="{{ url_for('admin_audit_logs', page=pagination.next_num, per_page=pagination.per_page) if pagination.has_next else '#' }}">
                                                    <i class="fas fa-angle-right"></i>
                                                </a>
                                            </li>
                                            
                                            <!-- Last Page -->
                                            <li class="page-item {{ 'disabled' if not pagination.has_next else '' }}">
                                                <a class="page-link" href="{{ url_for('admin_audit_logs', page=pagination.total_pages, per_page=pagination.per_page) if pagination.has_next else '#' }}">
                                                    <i class="fas fa-angle-double-right"></i>
                                                </a>
                                            </li>
                                        </ul>
                                        </div>
                                    </nav>
                                </div>
                            {% endif %}
                        {% else %}
                            <div class="text-center py-4">
                                <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                                <p class="text-muted">No audit logs found. Admin actions will appear here.</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function refreshLogs() {
            location.reload();
        }

        function changePerPage(perPage) {
            const urlParams = new URLSearchParams(window.location.search);
            urlParams.set('per_page', perPage);
            urlParams.set('page', '1'); // Reset to first page when changing per_page
            window.location.search = urlParams.toString();
        }

        function exportLogs() {
            // Create CSV content
            const table = document.getElementById('auditTable');
            if (!table) return;
            
            let csv = [];
            const rows = table.querySelectorAll('tr');
            
            for (let i = 0; i < rows.length; i++) {
                const row = [];
                const cols = rows[i].querySelectorAll('td, th');
                
                for (let j = 0; j < cols.length; j++) {
                    let cellText = cols[j].innerText.replace(/"/g, '""');
                    row.push('"' + cellText + '"');
                }
                csv.push(row.join(','));
            }
            
            // Download CSV
            const csvContent = csv.join('\n');
            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.setAttribute('hidden', '');
            a.setAttribute('href', url);
            a.setAttribute('download', 'audit_logs_' + new Date().toISOString().split('T')[0] + '.csv');
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
        }

        // Add some visual feedback for pagination
        document.addEventListener('DOMContentLoaded', function() {
            // Add loading state for pagination links
            const paginationLinks = document.querySelectorAll('.pagination .page-link');
            paginationLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    if (!this.closest('.page-item').classList.contains('disabled') && 
                        !this.closest('.page-item').classList.contains('active')) {
                        this.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                    }
                });
            });
        });
    </script>
</body>
</html>