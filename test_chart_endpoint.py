#!/usr/bin/env python3
"""
Test script to verify chart data endpoint functionality
"""

import requests
import json
import sys
import os

def test_chart_endpoint():
    """Test the chart data endpoint with different datasets"""
    
    # Base URL (adjust if needed)
    base_url = "http://localhost:5000"
    
    # Test datasets
    test_datasets = [
        'checkins_per_tutor',
        'hours_per_tutor', 
        'avg_session_duration_per_tutor',
        'tutor_consistency_score',
        'daily_checkins',
        'daily_hours',
        'cumulative_checkins',
        'cumulative_hours',
        'hourly_checkins_dist',
        'monthly_hours',
        'avg_hours_per_day_of_week',
        'checkins_per_day_of_week',
        'hourly_activity_by_day',
        'punctuality_analysis',
        'session_duration_distribution',
        'tutor_performance_scatter',
        'session_analysis_scatter',
        'session_efficiency_scatter'
    ]
    
    print("🧪 Testing Chart Data Endpoint")
    print("=" * 50)
    
    for dataset in test_datasets:
        print(f"\n📊 Testing dataset: {dataset}")
        
        # Test payload
        payload = {
            'chartKey': dataset,
            'start_date': '2025-01-01',
            'end_date': '2025-12-31'
        }
        
        try:
            response = requests.post(
                f"{base_url}/chart-data",
                json=payload,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                data = response.json()
                
                # Check if the dataset exists in response
                if dataset in data:
                    dataset_data = data[dataset]
                    if isinstance(dataset_data, dict):
                        data_count = len(dataset_data)
                        print(f"   ✅ Success: {data_count} data points")
                    elif isinstance(dataset_data, list):
                        data_count = len(dataset_data)
                        print(f"   ✅ Success: {data_count} data points")
                    else:
                        print(f"   ✅ Success: Data type {type(dataset_data)}")
                else:
                    print(f"   ❌ Error: Dataset '{dataset}' not found in response")
                    print(f"   Available datasets: {list(data.keys())}")
                    
            else:
                print(f"   ❌ HTTP Error: {response.status_code}")
                print(f"   Response: {response.text}")
                
        except requests.exceptions.ConnectionError:
            print(f"   ❌ Connection Error: Make sure the Flask app is running on {base_url}")
            break
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    print("\n" + "=" * 50)
    print("🏁 Testing complete!")

def test_tutor_consistency_score():
    """Test the tutor consistency score chart endpoint"""
    
    print("🧪 Testing Tutor Consistency Score Chart Endpoint")
    print("=" * 60)
    
    # Base URL (assuming Flask app is running on localhost:5000)
    base_url = "http://127.0.0.1:5000"
    
    # Test data for the chart endpoint
    test_payload = {
        "chartKey": "tutor_consistency_score",
        "chartType": "bar",
        "start_date": "2025-01-01",
        "end_date": "2025-12-31"
    }
    
    print(f"📤 Sending request to: {base_url}/chart-data")
    print(f"📋 Payload: {json.dumps(test_payload, indent=2)}")
    
    try:
        # Send POST request to chart-data endpoint
        response = requests.post(
            f"{base_url}/chart-data",
            json=test_payload,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"📥 Response status: {response.status_code}")
        print(f"📥 Response headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Success! Response data keys: {list(data.keys())}")
            
            # Check if tutor_consistency_score is in the response
            if "tutor_consistency_score" in data:
                consistency_data = data["tutor_consistency_score"]
                print(f"📊 Tutor consistency score data: {consistency_data}")
                print(f"📊 Number of tutors: {len(consistency_data)}")
                
                # Show some sample data
                for tutor, score in list(consistency_data.items())[:5]:
                    print(f"   {tutor}: {score}")
            else:
                print("❌ tutor_consistency_score not found in response")
                print(f"📋 Available keys: {list(data.keys())}")
                
        elif response.status_code == 401:
            print("❌ Authentication required - this is expected")
            print("💡 You need to be logged in to access the chart endpoint")
            print("💡 Try accessing the web interface at http://127.0.0.1:5000")
            
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"📋 Response text: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection error - is the Flask app running?")
        print("💡 Start the app with: python app.py")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_chart_endpoint()
    test_tutor_consistency_score() 