Implement multi-layout chart system with single, split, and grid views.

Fix filter functionality and advanced filters integration

- Add advanced filters to chart data requests via sessionStorage
- Fix form submission to include advanced filters in POST payload
- Add debug logging to track filter application
- Fix duration filter ranges (2+ -> 2-4, 4+)
- Add support for individual day filters (Monday-Sunday)
- Fix excludeWeekends and excludeHolidays boolean handling
- Add missing maxSessions filter implementation
- Enhance session pattern filters with high/low frequency options
- Fix timeOfDay filter ranges and add early_morning option
- Add outlier handling filter (exclude_extreme, cap_outliers)
- Improve advanced filter application to trigger chart refresh
- Fix clearAdvancedFilters to properly refresh charts

Fixes:
- Advanced filters not being sent to backend
- Basic filters not working with some data types
- Form submission missing sessionStorage data
- Boolean filter values not handled correctly
