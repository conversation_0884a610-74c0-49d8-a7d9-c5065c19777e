#!/usr/bin/env python3
"""
Script to check existing users in the database
"""

import os
import sys
from dotenv import load_dotenv

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
load_dotenv()

# Import auth module
from auth import supabase

def check_users():
    """Check existing users in the database"""
    print("Checking existing users...")
    
    try:
        # Try to get users from the custom users table
        response = supabase.table('users').select('*').execute()
        
        if response.data:
            print(f"Found {len(response.data)} users in custom users table:")
            for user in response.data:
                print(f"  - Email: {user.get('email', 'N/A')}")
                print(f"    Role: {user.get('role', 'N/A')}")
                print(f"    Tutor ID: {user.get('tutor_id', 'N/A')}")
                print(f"    Created: {user.get('created_at', 'N/A')}")
                print()
        else:
            print("No users found in custom users table")
            
    except Exception as e:
        print(f"Error checking custom users table: {e}")
    
    try:
        # Try to get users from Supabase auth
        auth_response = supabase.auth.admin.list_users()
        
        if auth_response and hasattr(auth_response, 'users'):
            print(f"Found {len(auth_response.users)} users in Supabase auth:")
            for user in auth_response.users:
                print(f"  - Email: {user.email}")
                print(f"    ID: {user.id}")
                print(f"    Created: {user.created_at}")
                print()
        else:
            print("No users found in Supabase auth")
            
    except Exception as e:
        print(f"Error checking Supabase auth users: {e}")

if __name__ == "__main__":
    check_users()
