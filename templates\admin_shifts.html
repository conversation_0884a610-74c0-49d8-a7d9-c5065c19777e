<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📅 Shift Management - Tutor Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="/static/style.css">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-user-graduate"></i> Tutor Dashboard
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('index') }}">
                    <i class="fas fa-tachometer-alt"></i> Dashboard
                </a>
                <a class="nav-link" href="{{ url_for('charts_page') }}">
                    <i class="fas fa-chart-bar"></i> Charts
                </a>
                <a class="nav-link" href="{{ url_for('admin_users') }}">
                    <i class="fas fa-users-cog"></i> Users
                </a>
                <a class="nav-link" href="{{ url_for('admin_audit_logs') }}">
                    <i class="fas fa-clipboard-list"></i> Audit Logs
                </a>
                <a class="nav-link active" href="{{ url_for('admin_shifts') }}">
                    <i class="fas fa-calendar-alt"></i> Shifts
                </a>
                <a class="nav-link" href="{{ url_for('logout') }}">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1><i class="fas fa-calendar-alt"></i> Shift Management</h1>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createShiftModal">
                        <i class="fas fa-plus"></i> Create Shift
                    </button>
                </div>

                <!-- Flash Messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else 'success' if category == 'success' else 'info' }} alert-dismissible fade show" role="alert">
                                <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' if category == 'success' else 'info-circle' }}"></i>
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <!-- Upcoming Shifts -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-clock"></i> Upcoming Shifts (Next 2 Weeks)</h5>
                            </div>
                            <div class="card-body">
                                {% if upcoming_shifts %}
                                    <div class="table-responsive">
                                        <table class="table table-sm table-striped">
                                            <thead>
                                                <tr>
                                                    <th>Date</th>
                                                    <th>Day</th>
                                                    <th>Shift</th>
                                                    <th>Time</th>
                                                    <th>Tutor</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for shift in upcoming_shifts %}
                                                    <tr>
                                                        <td>{{ shift.date }}</td>
                                                        <td>{{ shift.day_name }}</td>
                                                        <td>{{ shift.shift_name }}</td>
                                                        <td>{{ shift.start_time }} - {{ shift.end_time }}</td>
                                                        <td>
                                                            <strong>{{ shift.tutor_name }}</strong>
                                                            <br><small class="text-muted">ID: {{ shift.tutor_id }}</small>
                                                        </td>
                                                        <td>
                                                            <form method="POST" action="{{ url_for('admin_remove_assignment') }}" style="display: inline;">
                                                                <input type="hidden" name="assignment_id" value="{{ shift.assignment_id }}">
                                                                <button type="submit" class="btn btn-sm btn-outline-danger" 
                                                                        onclick="return confirm('Remove this tutor from the shift?')">
                                                                    <i class="fas fa-times"></i>
                                                                </button>
                                                            </form>
                                                        </td>
                                                    </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                {% else %}
                                    <div class="text-center py-3">
                                        <i class="fas fa-calendar-times fa-2x text-muted mb-2"></i>
                                        <p class="text-muted">No upcoming shifts scheduled.</p>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Shift Templates -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-list"></i> Shift Templates</h5>
                            </div>
                            <div class="card-body">
                                {% if shifts %}
                                    {% for shift in shifts %}
                                        <div class="card mb-3 {{ 'border-secondary' if not shift.active else '' }}">
                                            <div class="card-header d-flex justify-content-between align-items-center">
                                                <div>
                                                    <h6 class="mb-0">
                                                        {{ shift.shift_name }}
                                                        {% if not shift.active %}
                                                            <span class="badge bg-secondary">Inactive</span>
                                                        {% endif %}
                                                    </h6>
                                                    <small class="text-muted">
                                                        {{ shift.start_time }} - {{ shift.end_time }} | {{ shift.days_of_week }}
                                                    </small>
                                                </div>
                                                <div>
                                                    <button class="btn btn-sm btn-outline-primary" 
                                                            data-bs-toggle="modal" 
                                                            data-bs-target="#assignTutorModal"
                                                            data-shift-id="{{ shift.shift_id }}"
                                                            data-shift-name="{{ shift.shift_name }}">
                                                        <i class="fas fa-user-plus"></i> Assign Tutor
                                                    </button>
                                                    {% if shift.active %}
                                                        <form method="POST" action="{{ url_for('admin_deactivate_shift') }}" style="display: inline;">
                                                            <input type="hidden" name="shift_id" value="{{ shift.shift_id }}">
                                                            <button type="submit" class="btn btn-sm btn-outline-danger" 
                                                                    onclick="return confirm('Deactivate this shift and all assignments?')">
                                                                <i class="fas fa-ban"></i> Deactivate
                                                            </button>
                                                        </form>
                                                    {% endif %}
                                                </div>
                                            </div>
                                            {% if shift.assignments %}
                                                <div class="card-body">
                                                    <h6>Current Assignments:</h6>
                                                    <div class="row">
                                                        {% for assignment in shift.assignments %}
                                                            <div class="col-md-6 mb-2">
                                                                <div class="border rounded p-2">
                                                                    <strong>{{ assignment.tutor_name }}</strong> (ID: {{ assignment.tutor_id }})
                                                                    <br><small class="text-muted">
                                                                        {{ assignment.start_date }} to {{ assignment.end_date }}
                                                                    </small>
                                                                    <form method="POST" action="{{ url_for('admin_remove_assignment') }}" style="display: inline; float: right;">
                                                                        <input type="hidden" name="assignment_id" value="{{ assignment.assignment_id }}">
                                                                        <button type="submit" class="btn btn-sm btn-outline-danger" 
                                                                                onclick="return confirm('Remove this assignment?')">
                                                                            <i class="fas fa-times"></i>
                                                                        </button>
                                                                    </form>
                                                                </div>
                                                            </div>
                                                        {% endfor %}
                                                    </div>
                                                </div>
                                            {% endif %}
                                        </div>
                                    {% endfor %}
                                {% else %}
                                    <div class="text-center py-4">
                                        <i class="fas fa-calendar-plus fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">No shift templates created. Create your first shift to get started.</p>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Shift Modal -->
    <div class="modal fade" id="createShiftModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-plus"></i> Create New Shift</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="{{ url_for('admin_create_shift') }}">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="shift_name" class="form-label">Shift Name</label>
                            <input type="text" class="form-control" id="shift_name" name="shift_name" required>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="start_time" class="form-label">Start Time</label>
                                    <input type="time" class="form-control" id="start_time" name="start_time" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="end_time" class="form-label">End Time</label>
                                    <input type="time" class="form-control" id="end_time" name="end_time" required>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Days of Week</label>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="days_of_week" value="Monday" id="monday">
                                        <label class="form-check-label" for="monday">Monday</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="days_of_week" value="Tuesday" id="tuesday">
                                        <label class="form-check-label" for="tuesday">Tuesday</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="days_of_week" value="Wednesday" id="wednesday">
                                        <label class="form-check-label" for="wednesday">Wednesday</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="days_of_week" value="Thursday" id="thursday">
                                        <label class="form-check-label" for="thursday">Thursday</label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="days_of_week" value="Friday" id="friday">
                                        <label class="form-check-label" for="friday">Friday</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="days_of_week" value="Saturday" id="saturday">
                                        <label class="form-check-label" for="saturday">Saturday</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="days_of_week" value="Sunday" id="sunday">
                                        <label class="form-check-label" for="sunday">Sunday</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Create Shift</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Assign Tutor Modal -->
    <div class="modal fade" id="assignTutorModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-user-plus"></i> Assign Tutor to Shift</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="{{ url_for('admin_assign_shift') }}">
                    <div class="modal-body">
                        <input type="hidden" id="assign_shift_id" name="shift_id">
                        <div class="mb-3">
                            <label class="form-label">Shift</label>
                            <input type="text" class="form-control" id="assign_shift_name" readonly>
                        </div>
                        <div class="mb-3">
                            <label for="tutor_id" class="form-label">Tutor ID</label>
                            <input type="text" class="form-control" id="tutor_id" name="tutor_id" required>
                        </div>
                        <div class="mb-3">
                            <label for="tutor_name" class="form-label">Tutor Name</label>
                            <input type="text" class="form-control" id="tutor_name" name="tutor_name" required>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="start_date" class="form-label">Start Date</label>
                                    <input type="date" class="form-control" id="start_date" name="start_date" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="end_date" class="form-label">End Date (Optional)</label>
                                    <input type="date" class="form-control" id="end_date" name="end_date">
                                    <div class="form-text">Leave empty for ongoing assignment</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Assign Tutor</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Handle assign tutor modal
        document.getElementById('assignTutorModal').addEventListener('show.bs.modal', function (event) {
            const button = event.relatedTarget;
            const shiftId = button.getAttribute('data-shift-id');
            const shiftName = button.getAttribute('data-shift-name');
            
            document.getElementById('assign_shift_id').value = shiftId;
            document.getElementById('assign_shift_name').value = shiftName;
            
            // Set default start date to today
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('start_date').value = today;
        });
    </script>
</body>
</html>